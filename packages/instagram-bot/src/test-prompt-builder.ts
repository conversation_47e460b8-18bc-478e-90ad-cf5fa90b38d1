import { prisma } from '@workspace/database/client';
import type { PromptConfig, AdminPromptConfig } from './types';

/**
 * Get test settings for a specific user and organization
 */
export async function getTestPromptConfig(organizationId: string, userId: string): Promise<PromptConfig | null> {
  const testSettings = await prisma.testSettings.findFirst({
    where: {
      organizationId,
      userId
    },
    include: {
      botStyle: true
    }
  });

  if (!testSettings) {
    return null;
  }

  return {
    aboutUs: testSettings.aboutUs || undefined,
    qualificationQuestions: testSettings.qualificationQuestions || undefined,
    additionalInfo: testSettings.additionalInfo || undefined,
    botStyle: testSettings.botStyle
      ? {
        name: testSettings.botStyle.name,
        promptText: testSettings.botStyle.promptText
      }
      : undefined,
    youtubeLink: testSettings.youtubeLink || undefined,
    websiteLink: testSettings.websiteLink || undefined,
    leadMagnetLink: testSettings.leadMagnetLink || undefined,
    conversionLink: testSettings.conversionLink || ''
  };
}

/**
 * Get test admin prompt configuration for a specific user
 */
export async function getTestAdminPrompt(organizationId: string, userId: string): Promise<AdminPromptConfig | null> {
  const testAdminPrompt = await prisma.testAdminPrompt.findFirst({
    where: {
      organizationId,
      userId
    }
  });

  if (!testAdminPrompt) {
    return null;
  }

  return {
    generalPrompt: testAdminPrompt.generalPrompt,
    technicalPrompt: testAdminPrompt.technicalPrompt
  };
}

/**
 * Get admin prompt configuration (fallback to production if no test prompt)
 */
export async function getAdminPrompt(): Promise<AdminPromptConfig | null> {
  const adminPrompt = await prisma.adminPrompt.findFirst();

  if (!adminPrompt) {
    return null;
  }

  return {
    generalPrompt: adminPrompt.generalPrompt,
    technicalPrompt: adminPrompt.technicalPrompt
  };
}

/**
 * Build resources section from prompt config links
 */
function buildResourcesSection(config: PromptConfig): string {
  const resources = [];
  if (config.youtubeLink) resources.push(`YouTube: ${config.youtubeLink}`);
  if (config.websiteLink) resources.push(`Website: ${config.websiteLink}`);
  if (config.leadMagnetLink) resources.push(`Lead Magnet: ${config.leadMagnetLink}`);
  if (config.conversionLink) resources.push(`Conversion Link: ${config.conversionLink}`);

  return resources.length > 0 ? resources.join('\n') : '';
}

/**
 * Replace variables in text with actual values using template engine pattern
 */
function replaceVariables(text: string, variables: Record<string, string>): string {
  const VARIABLE_PATTERN = /\{\{(\w+)\}\}/g;
  return text.replace(VARIABLE_PATTERN, (match, variableName) => {
    const value = variables[variableName];
    if (value !== undefined && value !== null) {
      return value;
    }
    // Keep the original placeholder if no replacement found
    return match;
  });
}

/**
 * Build the complete test prompt using test settings
 */
export async function buildTestPrompt(organizationId: string, userId: string): Promise<string> {
  const [promptConfig, testAdminPrompt, adminPrompt] = await Promise.all([
    getTestPromptConfig(organizationId, userId),
    getTestAdminPrompt(organizationId, userId),
    getAdminPrompt()
  ]);

  // Use test admin prompt if available, otherwise fall back to production admin prompt
  const activeAdminPrompt = testAdminPrompt || adminPrompt;

  if (!activeAdminPrompt) {
    throw new Error('Missing admin prompt configuration');
  }

  // Use default values if test settings don't exist
  const config = promptConfig || {
    aboutUs: '',
    qualificationQuestions: '',
    additionalInfo: '',
    botStyle: undefined,
    youtubeLink: '',
    websiteLink: '',
    leadMagnetLink: '',
    conversionLink: ''
  };

  // Prepare variables for replacement in admin prompts
  const variables: Record<string, string> = {
    youtube_link: config.youtubeLink || '',
    website_link: config.websiteLink || '',
    lead_magnet_link: config.leadMagnetLink || '',
    conversion_link: config.conversionLink || '',
    GENERAL_PROMPT: activeAdminPrompt.generalPrompt,
    TECHNICAL_PROMPT: activeAdminPrompt.technicalPrompt,
    ABOUT_YOU: config.aboutUs || '',
    QUALIFICATION_QUESTIONS: config.qualificationQuestions || '',
    ADDITIONAL_INFO: config.additionalInfo || '',
    RESOURCES: buildResourcesSection(config),
    BOT_STYLE: config.botStyle?.promptText || ''
  };

  // Replace variables in admin prompts
  const generalPrompt = replaceVariables(activeAdminPrompt.generalPrompt, variables);
  const technicalPrompt = replaceVariables(activeAdminPrompt.technicalPrompt, variables);

  // Build the full prompt using new XML tag structure with priority
  let fullPrompt = `Most important things are in tags:
Tags priority:
CRITICAL: <general_rules>, <technical_rules>
REST: <about_you>, <qualification_questions>, <resources>, <bot_style>, <additional_info>

<general_rules>
${generalPrompt}
</general_rules>

<technical_rules>
${technicalPrompt}

TEST RESPONSE FORMAT:
For testing purposes, respond with a JSON object containing:
{
  "message1": "First message text",
  "message2": "Second message text (optional)",
  "message3": "Third message text (optional)",
  "message4": "Fourth message text (optional)",
  "stage": "new|initial|engaged|qualified|formsent|disqualified|converted",
  "reason": "Required when stage is 'disqualified' - explain why",
  "followUps": [
    {
      "message": "Follow-up message text",
      "delayHours": 24
    }
  ]
}

IMPORTANT TEST FORMATTING NOTES:
- Include 1-4 messages (message1, message2, message3, message4) as separate fields
- Each message will be displayed separately in the test interface
- Long messages should be split into multiple shorter messages
- Follow-up messages will be shown with their delay times
- Always include a reason when setting stage to "disqualified"
- This is a TEST ENVIRONMENT - responses will be formatted for readability
</technical_rules>`;

  // Add about us section if available
  if (config.aboutUs) {
    fullPrompt += `

<about_you>
${config.aboutUs}
</about_you>`;
  }

  // Add qualification questions if available
  if (config.qualificationQuestions) {
    fullPrompt += `

<qualification_questions>
${config.qualificationQuestions}
</qualification_questions>`;
  }

  // Add resources section
  const resourcesContent = buildResourcesSection(config);
  if (resourcesContent) {
    fullPrompt += `

<resources>
${resourcesContent}
</resources>`;
  }

  // Add bot style if available
  if (config.botStyle) {
    fullPrompt += `

<bot_style>
${config.botStyle.promptText}
</bot_style>`;
  }

  // Add additional info if available
  if (config.additionalInfo) {
    fullPrompt += `

<additional_info>
${config.additionalInfo}
</additional_info>`;
  }

  console.log('Built test XML-structured prompt for organization:', organizationId, 'user:', userId);
  return fullPrompt;
}
