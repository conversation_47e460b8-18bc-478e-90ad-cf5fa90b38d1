import { prisma } from '@workspace/database/client';
import { AdminPromptConfig, PromptConfig } from './types';

/**
 * Template engine for variable replacement
 */
export class PromptTemplateEngine {
  private static readonly VARIABLE_PATTERN = /\{\{(\w+)\}\}/g;

  /**
   * Replace variables in template with actual values
   */
  static replaceVariables(template: string, variables: Record<string, string>): string {
    return template.replace(this.VARIABLE_PATTERN, (match, variableName) => {
      const value = variables[variableName];
      if (value !== undefined && value !== null) {
        return value;
      }
      // Keep the original placeholder if no replacement found
      return match;
    });
  }

  /**
   * Extract variable names from template
   */
  static extractVariables(template: string): string[] {
    const variables: string[] = [];
    let match;
    while ((match = this.VARIABLE_PATTERN.exec(template)) !== null) {
      if (!variables.includes(match[1])) {
        variables.push(match[1]);
      }
    }
    return variables;
  }

  /**
   * Validate that all required variables are provided
   */
  static validateVariables(template: string, variables: Record<string, string>): string[] {
    const requiredVariables = this.extractVariables(template);
    const missingVariables: string[] = [];

    for (const variable of requiredVariables) {
      if (variables[variable] === undefined || variables[variable] === null) {
        missingVariables.push(variable);
      }
    }

    return missingVariables;
  }
}

export async function getPromptConfig(organizationId: string): Promise<PromptConfig | null> {
  const promptConfig = await prisma.promptConfig.findFirst({
    where: { organizationId },
    include: {
      botStyle: true
    }
  });

  if (!promptConfig) {
    return null;
  }

  return {
    aboutUs: promptConfig.aboutUs || undefined,
    qualificationQuestions: promptConfig.qualificationQuestions || undefined,
    additionalInfo: promptConfig.additionalInfo || undefined,
    botStyle: promptConfig.botStyle
      ? {
        name: promptConfig.botStyle.name,
        promptText: promptConfig.botStyle.promptText
      }
      : undefined,
    youtubeLink: promptConfig.youtubeLink || undefined,
    websiteLink: promptConfig.websiteLink || undefined,
    leadMagnetLink: promptConfig.leadMagnetLink || undefined,
    conversionLink: promptConfig.conversionLink
  };
}

export async function getAdminPrompt(): Promise<AdminPromptConfig | null> {
  const adminPrompt = await prisma.adminPrompt.findFirst();

  if (!adminPrompt) {
    return null;
  }

  return {
    generalPrompt: adminPrompt.generalPrompt,
    technicalPrompt: adminPrompt.technicalPrompt,
    conversationGatheringPrompt: adminPrompt.conversationGatheringPrompt || undefined
  };
}

/**
 * Build resources section from prompt config links
 */
function buildResourcesSection(promptConfig: PromptConfig): string {
  const resources = [];
  if (promptConfig.youtubeLink) resources.push(`YouTube: ${promptConfig.youtubeLink}`);
  if (promptConfig.websiteLink) resources.push(`Website: ${promptConfig.websiteLink}`);
  if (promptConfig.leadMagnetLink) resources.push(`Lead Magnet: ${promptConfig.leadMagnetLink}`);
  if (promptConfig.conversionLink) resources.push(`Conversion Link: ${promptConfig.conversionLink}`);

  return resources.length > 0 ? resources.join('\n') : '';
}

/**
 * Build the conversation gathering prompt for analyzing existing conversations
 * Uses generalPrompt + technicalPrompt + conversationGatheringPrompt
 */
export async function buildConversationGatheringPrompt(organizationId: string): Promise<string> {
  const [promptConfig, adminPrompt] = await Promise.all([
    getPromptConfig(organizationId),
    getAdminPrompt()
  ]);

  if (!promptConfig || !adminPrompt) {
    throw new Error('Missing prompt configuration');
  }

  // Prepare variables for replacement in admin prompts
  const variables: Record<string, string> = {
    youtube_link: promptConfig.youtubeLink || '',
    website_link: promptConfig.websiteLink || '',
    lead_magnet_link: promptConfig.leadMagnetLink || '',
    conversion_link: promptConfig.conversionLink || '',
    GENERAL_PROMPT: adminPrompt.generalPrompt,
    TECHNICAL_PROMPT: adminPrompt.technicalPrompt,
    CONVERSATION_GATHERING_PROMPT: adminPrompt.conversationGatheringPrompt || '',
    ABOUT_YOU: promptConfig.aboutUs || '',
    QUALIFICATION_QUESTIONS: promptConfig.qualificationQuestions || '',
    ADDITIONAL_INFO: promptConfig.additionalInfo || '',
    RESOURCES: buildResourcesSection(promptConfig),
    BOT_STYLE: promptConfig.botStyle?.promptText || ''
  };

  // Replace variables in all prompts using the template engine
  const processedGeneralPrompt = PromptTemplateEngine.replaceVariables(
    adminPrompt.generalPrompt,
    variables
  );

  const processedTechnicalPrompt = PromptTemplateEngine.replaceVariables(
    adminPrompt.technicalPrompt,
    variables
  );

  const processedConversationGatheringPrompt = adminPrompt.conversationGatheringPrompt
    ? PromptTemplateEngine.replaceVariables(adminPrompt.conversationGatheringPrompt, variables)
    : '';

  // Also support legacy variable format for backward compatibility
  let generalPrompt = processedGeneralPrompt;
  let technicalPrompt = processedTechnicalPrompt;
  let conversationGatheringPrompt = processedConversationGatheringPrompt;

  if (promptConfig.youtubeLink) {
    generalPrompt = generalPrompt.replace('$youtube', promptConfig.youtubeLink);
    technicalPrompt = technicalPrompt.replace('$youtube', promptConfig.youtubeLink);
    conversationGatheringPrompt = conversationGatheringPrompt.replace('$youtube', promptConfig.youtubeLink);
  }

  if (promptConfig.websiteLink) {
    generalPrompt = generalPrompt.replace('$website', promptConfig.websiteLink);
    technicalPrompt = technicalPrompt.replace('$website', promptConfig.websiteLink);
    conversationGatheringPrompt = conversationGatheringPrompt.replace('$website', promptConfig.websiteLink);
  }

  if (promptConfig.leadMagnetLink) {
    generalPrompt = generalPrompt.replace('$lead_magnet', promptConfig.leadMagnetLink);
    technicalPrompt = technicalPrompt.replace('$lead_magnet', promptConfig.leadMagnetLink);
    conversationGatheringPrompt = conversationGatheringPrompt.replace('$lead_magnet', promptConfig.leadMagnetLink);
  }

  generalPrompt = generalPrompt.replace('$conversion_link', promptConfig.conversionLink);
  technicalPrompt = technicalPrompt.replace('$conversion_link', promptConfig.conversionLink);
  conversationGatheringPrompt = conversationGatheringPrompt.replace('$conversion_link', promptConfig.conversionLink);

  // Build the conversation gathering prompt using XML tag structure with priority
  let fullPrompt = `Most important things are in tags:
Tags priority:
CRITICAL: <general_rules>, <technical_rules>, <conversation_gathering_rules>
REST: <about_you>, <qualification_questions>, <resources>, <bot_style>, <additional_info>

<general_rules>
${generalPrompt}
</general_rules>

<technical_rules>
${technicalPrompt}
</technical_rules>`;

  // Add conversation gathering rules with essential instructions
  fullPrompt += `

<conversation_gathering_rules>
CONVERSATION GATHERING MODE: You are analyzing an existing conversation to determine follow-up strategy.

CRITICAL RULES:
- The user has NOT sent you a new message
- You are analyzing conversation history only
- DO NOT send immediate messages (message1, message2, message3, message4 should be null)
- ONLY set follow-up messages (fu1_message, fu2_message, etc) with appropriate timing
- Focus on analyzing conversation quality and engagement level

CONVERSATION HISTORY FORMAT:
User: [user message]
Ja: [your previous response]
User: [user message]
...

${conversationGatheringPrompt || 'Please analyze the conversation and set appropriate follow-up strategy based on engagement level and conversation quality.'}
</conversation_gathering_rules>`;

  // Add JSON format requirement
  fullPrompt += `

RESPONSE FORMAT:
You must respond with valid JSON in the following format:
{
  "stage": "new|initial|engaged|qualified|formsent|disqualified|converted",
  "priority": 1-5 (1=highest priority, 5=lowest priority),
  "message1": null,
  "message2": null,
  "message3": null,
  "message4": null,
  "fu1_message": "First follow-up message",
  "fu1_time": "Timestamp for first follow-up (format: 2025-02-20T11:43:54.865+01:00)",
  "fu1_status": "pending",
  "fu2_message": "Second follow-up message (optional)",
  "fu2_time": "Timestamp for second follow-up (optional)",
  "fu2_status": "pending",
  "fu3_message": "Third follow-up message (optional)",
  "fu3_time": "Timestamp for third follow-up (optional)",
  "fu3_status": "pending",
  "fu4_message": "Fourth follow-up message (optional)",
  "fu4_time": "Timestamp for fourth follow-up (optional)",
  "fu4_status": "pending",
  "reason": "Reason for stage change (required for disqualified stage)"
}

- The timestamp format must be ISO 8601 (e.g., 2025-02-20T11:43:54.865+01:00)
- Always include a reason when setting stage to "disqualified"
- Never respond with plain text. Always return valid JSON even for old conversations.`;

  // Add about us section if available
  if (promptConfig.aboutUs) {
    fullPrompt += `

<about_you>
${promptConfig.aboutUs}
</about_you>`;
  }

  // Add qualification questions if available
  if (promptConfig.qualificationQuestions) {
    fullPrompt += `

<qualification_questions>
${promptConfig.qualificationQuestions}
</qualification_questions>`;
  }

  // Add resources section
  const resourcesContent = buildResourcesSection(promptConfig);
  if (resourcesContent) {
    fullPrompt += `

<resources>
${resourcesContent}
</resources>`;
  }

  // Add bot style if available
  if (promptConfig.botStyle) {
    fullPrompt += `

<bot_style>
${promptConfig.botStyle.promptText}
</bot_style>`;
  }

  // Add additional info if available
  if (promptConfig.additionalInfo) {
    fullPrompt += `

<additional_info>
${promptConfig.additionalInfo}
</additional_info>`;
  }

  console.log('Built conversation gathering prompt for organization:', organizationId);
  return fullPrompt;
}

/**
 * Build the complete prompt using new XML tag structure (no internal caching - only Anthropic caching)
 */
export async function buildFullPrompt(organizationId: string): Promise<string> {
  const [promptConfig, adminPrompt] = await Promise.all([
    getPromptConfig(organizationId),
    getAdminPrompt()
  ]);

  if (!promptConfig || !adminPrompt) {
    throw new Error('Missing prompt configuration');
  }

  // Prepare variables for replacement in admin prompts
  const variables: Record<string, string> = {
    youtube_link: promptConfig.youtubeLink || '',
    website_link: promptConfig.websiteLink || '',
    lead_magnet_link: promptConfig.leadMagnetLink || '',
    conversion_link: promptConfig.conversionLink || '',
    GENERAL_PROMPT: adminPrompt.generalPrompt,
    TECHNICAL_PROMPT: adminPrompt.technicalPrompt,
    ABOUT_YOU: promptConfig.aboutUs || '',
    QUALIFICATION_QUESTIONS: promptConfig.qualificationQuestions || '',
    ADDITIONAL_INFO: promptConfig.additionalInfo || '',
    RESOURCES: buildResourcesSection(promptConfig),
    BOT_STYLE: promptConfig.botStyle?.promptText || ''
  };

  console.log('Variable replacement values:', variables);

  // Replace variables in both general and technical prompts using the template engine
  const processedGeneralPrompt = PromptTemplateEngine.replaceVariables(
    adminPrompt.generalPrompt,
    variables
  );

  const processedTechnicalPrompt = PromptTemplateEngine.replaceVariables(
    adminPrompt.technicalPrompt,
    variables
  );

  // Also support legacy variable format for backward compatibility
  let generalPrompt = processedGeneralPrompt;
  let technicalPrompt = processedTechnicalPrompt;

  if (promptConfig.youtubeLink) {
    generalPrompt = generalPrompt.replace('$youtube', promptConfig.youtubeLink);
    technicalPrompt = technicalPrompt.replace('$youtube', promptConfig.youtubeLink);
  }
  if (promptConfig.websiteLink) {
    generalPrompt = generalPrompt.replace('$website', promptConfig.websiteLink);
    technicalPrompt = technicalPrompt.replace('$website', promptConfig.websiteLink);
  }
  if (promptConfig.leadMagnetLink) {
    generalPrompt = generalPrompt.replace('$lead_magnet', promptConfig.leadMagnetLink);
    technicalPrompt = technicalPrompt.replace('$lead_magnet', promptConfig.leadMagnetLink);
  }

  generalPrompt = generalPrompt.replace('$conversion_link', promptConfig.conversionLink);
  technicalPrompt = technicalPrompt.replace('$conversion_link', promptConfig.conversionLink);

  // Build the full prompt using new XML tag structure with priority
  let fullPrompt = `Most important things are in tags:
Tags priority:
CRITICAL: <general_rules>, <technical_rules>
REST: <about_you>, <qualification_questions>, <resources>, <bot_style>, <additional_info>

<general_rules>
${generalPrompt}
</general_rules>

<technical_rules>
${technicalPrompt}

RESPONSE FORMAT:
You must respond with valid JSON in the following format:
{
  "stage": "new|initial|engaged|qualified|formsent|disqualified|converted",
  "priority": 1-5 (1=highest priority, 5=lowest priority),
  "message1": "First message to send",
  "message2": "Second message to send (optional)",
  "message3": "Third message to send (optional)",
  "message4": "Fourth message to send (optional)",
  "slack_message": "Alert message for Slack (optional)",
  "fu1_message": "First follow-up message",
  "fu1_time": "Timestamp for first follow-up (format: 2025-02-20T11:43:54.865+01:00)",
  "fu1_status": "pending",
  "fu2_message": "Second follow-up message (optional)",
  "fu2_time": "Timestamp for second follow-up (optional)",
  "fu2_status": "pending",
  "fu3_message": "Third follow-up message (optional)",
  "fu3_time": "Timestamp for third follow-up (optional)",
  "fu3_status": "pending",
  "fu4_message": "Fourth follow-up message (optional)",
  "fu4_time": "Timestamp for fourth follow-up (optional)",
  "fu4_status": "pending",
  "reason": "Reason for stage change (required for disqualified stage)"
}

- The timestamp format must be ISO 8601 (e.g., 2025-02-20T11:43:54.865+01:00)
- Always include a reason when setting stage to "disqualified"
</technical_rules>`;

  // Add about us section if available
  if (promptConfig.aboutUs) {
    fullPrompt += `

<about_you>
${promptConfig.aboutUs}
</about_you>`;
  }

  // Add qualification questions if available
  if (promptConfig.qualificationQuestions) {
    fullPrompt += `

<qualification_questions>
${promptConfig.qualificationQuestions}
</qualification_questions>`;
  }

  // Add resources section
  const resourcesContent = buildResourcesSection(promptConfig);
  if (resourcesContent) {
    fullPrompt += `

<resources>
${resourcesContent}
</resources>`;
  }

  // Add bot style if available
  if (promptConfig.botStyle) {
    fullPrompt += `

<bot_style>
${promptConfig.botStyle.promptText}
</bot_style>`;
  }

  // Add additional info if available
  if (promptConfig.additionalInfo) {
    fullPrompt += `

<additional_info>
${promptConfig.additionalInfo}
</additional_info>`;
  }

  console.log('Built new XML-structured prompt for organization:', organizationId);
  return fullPrompt;
}
