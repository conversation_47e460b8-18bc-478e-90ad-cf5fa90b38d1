'use client';

import * as React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@workspace/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@workspace/ui/components/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@workspace/ui/components/form';
import { Input } from '@workspace/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@workspace/ui/components/select';
import { Textarea } from '@workspace/ui/components/textarea';
import { useToast } from '@workspace/ui/hooks/use-toast';

import { PromptConfigDto } from '~/types/dtos/prompt-config-dto';
import { BotStyleDto } from '~/types/dtos/bot-style-dto';
import { savePromptConfig } from '~/actions/instagram/save-prompt-config';

const promptConfigSchema = z.object({
  aboutUs: z.string().optional(),
  qualificationQuestions: z.string().optional(),
  additionalInfo: z.string().optional(),
  botStyleId: z.string().uuid().optional(),
  youtubeLink: z.string().url('Please enter a valid YouTube URL').optional().or(z.literal('')),
  websiteLink: z.string().url('Please enter a valid website URL').optional().or(z.literal('')),
  leadMagnetLink: z.string().url('Please enter a valid lead magnet URL').optional().or(z.literal('')),
  conversionLink: z.string().url('Please enter a valid conversion URL').optional().or(z.literal(''))
});

type PromptConfigFormValues = z.infer<typeof promptConfigSchema>;

interface PromptConfigFormProps {
  promptConfig: PromptConfigDto | null;
  botStyles: BotStyleDto[];
}

export function PromptConfigForm({ promptConfig, botStyles }: PromptConfigFormProps): React.JSX.Element {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = React.useState(false);

  const form = useForm<PromptConfigFormValues>({
    resolver: zodResolver(promptConfigSchema),
    defaultValues: {
      aboutUs: promptConfig?.aboutUs || '',
      qualificationQuestions: promptConfig?.qualificationQuestions || '',
      additionalInfo: promptConfig?.additionalInfo || '',
      botStyleId: promptConfig?.botStyleId || '',
      youtubeLink: promptConfig?.youtubeLink || '',
      websiteLink: promptConfig?.websiteLink || '',
      leadMagnetLink: promptConfig?.leadMagnetLink || '',
      conversionLink: promptConfig?.conversionLink || ''
    }
  });

  const onSubmit = async (values: PromptConfigFormValues) => {
    setIsLoading(true);
    try {
      await savePromptConfig({
        id: promptConfig?.id,
        ...values
      });

      toast({
        title: 'Prompt configuration saved',
        description: 'Your prompt configuration has been saved successfully'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save prompt configuration',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Bot Content</CardTitle>
              <CardDescription>
                Configure the content your bot will use when communicating with leads
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="botStyleId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bot Style</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a bot style" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {botStyles.map((style) => (
                          <SelectItem key={style.id} value={style.id}>
                            {style.name} {style.isDefault && '(Default)'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose the communication style for your bot
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="aboutUs"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>About Us</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Tell us about your business..."
                        className="min-h-32"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide information about your business that the bot can use in conversations
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="qualificationQuestions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Qualification Questions</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter qualification questions..."
                        className="min-h-32"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      List questions the bot should ask to qualify leads (one per line)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="additionalInfo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Additional Information</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any additional information..."
                        className="min-h-32"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Any other information the bot should know about your business
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Resources</CardTitle>
              <CardDescription>
                Configure the resources your bot can share with leads
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="youtubeLink"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>YouTube Link</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://youtube.com/..."
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      A YouTube video that showcases your business or service
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="websiteLink"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website Link</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://example.com"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Your business website
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="leadMagnetLink"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lead Magnet Link</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://example.com/lead-magnet"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      A free resource to offer leads (e.g., PDF guide, webinar)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="conversionLink"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Conversion Link</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://example.com/book-appointment"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      The link to your booking page or main conversion goal
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : 'Save Configuration'}
            </Button>
          </div>
        </div>
      </form>
    </Form >
  );
}
