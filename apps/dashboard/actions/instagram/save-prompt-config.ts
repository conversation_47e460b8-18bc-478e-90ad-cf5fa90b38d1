'use server';

import { z } from 'zod';

import { prisma } from '@workspace/database/client';

import { authOrganizationActionClient } from '~/actions/safe-action';

const savePromptConfigSchema = z.object({
  id: z.string().uuid().optional(),
  aboutUs: z.string().optional(),
  qualificationQuestions: z.string().optional(),
  additionalInfo: z.string().optional(),
  botStyleId: z.string().uuid().optional(),
  youtubeLink: z.string().url().optional().or(z.literal('')),
  websiteLink: z.string().url().optional().or(z.literal('')),
  leadMagnetLink: z.string().url().optional().or(z.literal('')),
  conversionLink: z.string().url().optional().or(z.literal(''))
});

export const savePromptConfig = authOrganizationActionClient
  .metadata({ actionName: 'savePromptConfig' })
  .schema(savePromptConfigSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Check if a prompt config already exists for this organization
    const existingConfig = await prisma.promptConfig.findFirst({
      where: { organizationId: ctx.organization.id },
      select: { id: true }
    });

    if (existingConfig) {
      // Update existing config
      await prisma.promptConfig.update({
        where: { id: existingConfig.id },
        data: {
          aboutUs: parsedInput.aboutUs || null,
          qualificationQuestions: parsedInput.qualificationQuestions || null,
          additionalInfo: parsedInput.additionalInfo || null,
          botStyleId: parsedInput.botStyleId || null,
          youtubeLink: parsedInput.youtubeLink || null,
          websiteLink: parsedInput.websiteLink || null,
          leadMagnetLink: parsedInput.leadMagnetLink || null,
          conversionLink: parsedInput.conversionLink || ''
        }
      });
    } else {
      // Create new config
      await prisma.promptConfig.create({
        data: {
          organizationId: ctx.organization.id,
          aboutUs: parsedInput.aboutUs || null,
          qualificationQuestions: parsedInput.qualificationQuestions || null,
          additionalInfo: parsedInput.additionalInfo || null,
          botStyleId: parsedInput.botStyleId || null,
          youtubeLink: parsedInput.youtubeLink || null,
          websiteLink: parsedInput.websiteLink || null,
          leadMagnetLink: parsedInput.leadMagnetLink || null,
          conversionLink: parsedInput.conversionLink || ''
        }
      });
    }

    // Cache invalidation removed - Prompt config no longer cached

    return { success: true };
  });
