export const MessageTypes = {
    GET_COOKIE: "getCookie",
    TEST_TAB_BUTTON: 'testTabButton',
    SAVE_SETTINGS: 'saveSettings',
    GET_SETTINGS: 'getSettings',
    GET_STATUS: 'getStatus',
    START_BOT: 'startBot',
    STOP_BOT: 'stopBot',
    START_PROCESS: 'startProcess',
    STOP_PROCESS: 'stopProcess',
    RESET_DM_POOL: 'resetDMPool',
    GET_INSTA_INBOX: 'getInstaInbox',
    HAVE_ZERO_MESSAGES: 'haveZeroMessages',
    VISIT_USER_MESSAGES: 'visitUserMessages',
    MESSAGE_PROFILE: 'messageProfile',
    // UI Navigation Flow
    NAVIGATE_TO_PROFILE: 'navigateToProfile',
    CLICK_MESSAGE_BUTTON: 'clickMessageButton',
    SEND_DIRECT_MESSAGE: 'sendDirectMessage',
    RESET_NONZERO_CACHE: 'resetNon<PERSON>eroCache',
    GET_PROFILE_URL: 'getProfileUrl',
    CLICK_PROFILE_ELEMENT: 'clickProfileElement',
    OPEN_FOLLOWERS_PANEL: 'openFollowersPanel',
    GET_FOLLOWERS_LIST_BY_COUNT: 'getFollowersList',
    GET_INITIAL_FOLLOWERS: 'getInitialFollowers',
    // API Key functionality
    SAVE_API_KEY: 'saveApiKey',
    GET_API_KEY: 'getApiKey',
    VERIFY_API_KEY: 'verifyApiKey',
    GET_API_STATUS: 'getApiStatus',
    // Follow-up functionality
    FETCH_PENDING_FOLLOWUPS: 'fetchPendingFollowUps',
    SEND_FOLLOWUP_MESSAGE: 'sendFollowUpMessage',
    MARK_FOLLOWUP_SENT: 'markFollowUpSent',
    MARK_FOLLOWUP_FAILED: 'markFollowUpFailed',
    // Auto-DM functionality
    SET_AUTO_DM_ENABLED: 'setAutoDMEnabled',
    GET_AUTO_DM_STATUS: 'getAutoDMStatus',
    // Follower scraping functionality
    CHECK_SCRAPING_STATUS: 'checkScrapingStatus',
    START_FOLLOWER_SCRAPING: 'startFollowerScraping',
    SCRAPE_FOLLOWERS_BATCH: 'scrapeFollowersBatch',
    SEND_SCRAPED_FOLLOWERS: 'sendScrapedFollowers',
    RESET_SCRAPING_PROGRESS: 'resetScrapingProgress',
    GET_SCRAPING_PROGRESS: 'getScrapingProgress',
    CLEAR_SCRAPING_WAIT: 'clearScrapingWait',
    // Conversation gathering functionality
    CHECK_CONVERSATION_GATHERING_STATUS: 'checkConversationGatheringStatus',
    POLL_CONVERSATION_GATHERING: 'pollConversationGathering',
    // Utility messages
    PING: 'ping',
    CLOSE_DIALOG: 'closeDialog',
    MIMIC_SCROLL: 'mimicScroll'
}